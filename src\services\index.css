@import url("https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    min-height: 100vh;
    font-family: "Inter", sans-serif;
    background: linear-gradient(to bottom right, #fffdf0, #ffffff, #f0fdf4);
    color: #43302b;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: "Playfair Display", serif;
  }
}

@layer components {
  .wedding-gradient {
    background: linear-gradient(to right, #ffd21a, #f59e0b, #16a34a);
  }

  .wedding-text-gradient {
    background: linear-gradient(to right, #d4a50a, #b45309, #15803d);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  .glass-effect {
    background: rgba(255, 253, 240, 0.85);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 210, 26, 0.2);
    box-shadow: 0 25px 50px -12px rgba(67, 48, 43, 0.15);
  }

  .btn-primary {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(to right, #ffd21a, #f59e0b);
    color: #43302b;
    font-weight: 600;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(212, 165, 10, 0.3);
    transition: all 0.2s;
    outline: none;
  }

  .btn-primary:hover {
    background: linear-gradient(to right, #f5c211, #d97706);
    transform: translateY(-1px);
    box-shadow: 0 15px 25px -5px rgba(212, 165, 10, 0.4);
  }

  .btn-secondary {
    padding: 0.75rem 1.5rem;
    background: rgba(255, 253, 240, 0.9);
    color: #43302b;
    font-weight: 500;
    border-radius: 0.5rem;
    border: 2px solid #ffd21a;
    box-shadow: 0 4px 6px -1px rgba(212, 165, 10, 0.1);
    transition: all 0.2s;
    outline: none;
  }

  .btn-secondary:hover {
    background: #ffd21a;
    transform: translateY(-1px);
    box-shadow: 0 8px 15px -3px rgba(212, 165, 10, 0.3);
  }
}

import { useState, useEffect, useCallback } from "react";
import {
  GoogleDriveService,
  type GoogleDriveConfig,
  type UploadResult,
} from "../services/googleDriveService";

export interface GoogleDriveState {
  isInitialized: boolean;
  isSignedIn: boolean;
  isUploading: boolean;
  uploadProgress: {
    completed: number;
    total: number;
    currentFile: string;
  } | null;
  error: string | null;
}

export interface UseGoogleDriveReturn {
  state: GoogleDriveState;
  service: GoogleDriveService | null;
  initialize: () => Promise<boolean>;
  signIn: () => Promise<boolean>;
  signOut: () => Promise<void>;
  uploadFiles: (files: File[]) => Promise<UploadResult[]>;
  clearError: () => void;
}

export function useGoogleDrive(
  config?: GoogleDriveConfig,
): UseGoogleDriveReturn {
  const [state, setState] = useState<GoogleDriveState>({
    isInitialized: false,
    isSignedIn: false,
    isUploading: false,
    uploadProgress: null,
    error: null,
  });

  const [service, setService] = useState<GoogleDriveService | null>(null);

  // Initialize service
  useEffect(() => {
    // Create default config from environment variables
    const defaultConfig: GoogleDriveConfig = {
      apiKey: import.meta.env.REACT_APP_GOOGLE_API_KEY || "",
      clientId: import.meta.env.REACT_APP_GOOGLE_CLIENT_ID || "",
      folderId: import.meta.env.REACT_APP_GOOGLE_DRIVE_FOLDER_ID || "",
    };

    const driveConfig = config || defaultConfig;

    // Check if configuration is available
    if (!driveConfig.apiKey || !driveConfig.clientId || !driveConfig.folderId) {
      setState((prev) => ({
        ...prev,
        error:
          "Google Drive configuration is missing. Please check environment variables.",
      }));
      return;
    }

    const driveService = new GoogleDriveService(driveConfig);
    setService(driveService);
  }, [config]);

  const initialize = useCallback(async (): Promise<boolean> => {
    if (!service) {
      setState((prev) => ({
        ...prev,
        error: "Google Drive service not available",
      }));
      return false;
    }

    setState((prev) => ({ ...prev, error: null }));

    try {
      const success = await service.initialize();

      setState((prev) => ({
        ...prev,
        isInitialized: success,
        isSignedIn: success ? service.isSignedIn() : false,
        error: success ? null : "Failed to initialize Google Drive API",
      }));

      return success;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Initialization failed";
      setState((prev) => ({ ...prev, error: errorMessage }));
      return false;
    }
  }, [service]);

  const signIn = useCallback(async (): Promise<boolean> => {
    if (!service || !state.isInitialized) {
      setState((prev) => ({ ...prev, error: "Google Drive not initialized" }));
      return false;
    }

    setState((prev) => ({ ...prev, error: null }));

    try {
      const success = await service.signIn();
      setState((prev) => ({ ...prev, isSignedIn: success }));
      return success;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Sign in failed";
      setState((prev) => ({ ...prev, error: errorMessage }));
      return false;
    }
  }, [service, state.isInitialized]);

  const signOut = useCallback(async (): Promise<void> => {
    if (!service) return;

    try {
      await service.signOut();
      setState((prev) => ({ ...prev, isSignedIn: false }));
    } catch (error) {
      console.error("Sign out failed:", error);
    }
  }, [service]);

  const uploadFiles = useCallback(
    async (files: File[]): Promise<UploadResult[]> => {
      if (!service || !state.isInitialized || !state.isSignedIn) {
        const error = "Google Drive not ready for upload";
        setState((prev) => ({ ...prev, error }));
        return files.map((file) => ({
          success: false,
          fileName: file.name,
          error,
        }));
      }

      setState((prev) => ({
        ...prev,
        isUploading: true,
        error: null,
        uploadProgress: { completed: 0, total: files.length, currentFile: "" },
      }));

      try {
        const results = await service.uploadFiles(
          files,
          (completed, total, currentFile) => {
            setState((prev) => ({
              ...prev,
              uploadProgress: { completed, total, currentFile },
            }));
          },
        );

        setState((prev) => ({
          ...prev,
          isUploading: false,
          uploadProgress: null,
        }));

        return results;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Upload failed";
        setState((prev) => ({
          ...prev,
          isUploading: false,
          uploadProgress: null,
          error: errorMessage,
        }));

        return files.map((file) => ({
          success: false,
          fileName: file.name,
          error: errorMessage,
        }));
      }
    },
    [service, state.isInitialized, state.isSignedIn],
  );

  const clearError = useCallback(() => {
    setState((prev) => ({ ...prev, error: null }));
  }, []);

  return {
    state,
    service,
    initialize,
    signIn,
    signOut,
    uploadFiles,
    clearError,
  };
}

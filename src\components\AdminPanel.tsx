import { useState, useEffect } from "react";

interface ProcessedFile {
  original: File;
  compressed?: File;
  compressionResult?: any;
}

interface AdminPanelProps {
  isOpen: boolean;
  onClose: () => void;
  processedFiles: ProcessedFile[];
  onRemoveFile: (index: number) => void;
  uploadsEnabled: boolean;
  onToggleUploads: (enabled: boolean) => void;
}

// Simple admin password - in production, this should be more secure
const ADMIN_PASSWORD =
  import.meta.env.REACT_APP_ADMIN_PASSWORD || "wedding2025";

export const AdminPanel: React.FC<AdminPanelProps> = ({
  isOpen,
  onClose,
  processedFiles,
  onRemoveFile,
  uploadsEnabled,
  onToggleUploads,
}) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [password, setPassword] = useState("");
  const [loginError, setLoginError] = useState("");
  const [showConfirmDialog, setShowConfirmDialog] = useState<number | null>(
    null,
  );

  // Reset authentication when panel closes
  useEffect(() => {
    if (!isOpen) {
      setIsAuthenticated(false);
      setPassword("");
      setLoginError("");
      setShowConfirmDialog(null);
    }
  }, [isOpen]);

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();

    if (password === ADMIN_PASSWORD) {
      setIsAuthenticated(true);
      setLoginError("");
    } else {
      setLoginError("Incorrect password. Please try again.");
      setPassword("");
    }
  };

  const handleRemoveFile = (index: number) => {
    onRemoveFile(index);
    setShowConfirmDialog(null);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b border-gray-200 bg-sunflower-50">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-serif text-brown-800">
              {isAuthenticated ? "Admin Panel" : "Admin Login"}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 p-2"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div
          className="p-6 overflow-y-auto"
          style={{ maxHeight: "calc(90vh - 140px)" }}
        >
          {!isAuthenticated ? (
            /* Login Form */
            <div className="max-w-md mx-auto">
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-sunflower-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg
                    className="w-8 h-8 text-amber-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                    />
                  </svg>
                </div>
                <p className="text-gray-600">
                  Enter the admin password to access the control panel.
                </p>
              </div>

              <form onSubmit={handleLogin} className="space-y-4">
                <div>
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Password
                  </label>
                  <input
                    type="password"
                    id="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sunflower-500 focus:border-sunflower-500"
                    placeholder="Enter admin password"
                    required
                  />
                </div>

                {loginError && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-700 text-sm">{loginError}</p>
                  </div>
                )}

                <button type="submit" className="w-full btn-primary">
                  Login
                </button>
              </form>

              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-blue-800 text-sm">
                  <strong>Default Password:</strong> wedding2025
                  <br />
                  <span className="text-xs text-blue-600">
                    Change this by setting REACT_APP_ADMIN_PASSWORD environment
                    variable
                  </span>
                </p>
              </div>
            </div>
          ) : (
            /* Admin Dashboard */
            <div className="space-y-6">
              {/* Upload Controls */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                  Upload Controls
                </h3>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-700">File Uploads</p>
                    <p className="text-sm text-gray-500">
                      {uploadsEnabled
                        ? "Guests can upload files"
                        : "File uploads are disabled"}
                    </p>
                  </div>
                  <button
                    onClick={() => onToggleUploads(!uploadsEnabled)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      uploadsEnabled ? "bg-sunflower-500" : "bg-gray-300"
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        uploadsEnabled ? "translate-x-6" : "translate-x-1"
                      }`}
                    />
                  </button>
                </div>
              </div>

              {/* File Management */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                  Uploaded Files ({processedFiles.length})
                </h3>

                {processedFiles.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">
                    No files uploaded yet.
                  </p>
                ) : (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {processedFiles.map((processedFile, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-4 bg-white rounded-lg border"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-sunflower-100 rounded-lg flex items-center justify-center">
                            {processedFile.original.type.startsWith(
                              "image/",
                            ) ? (
                              <svg
                                className="w-5 h-5 text-amber-600"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            ) : (
                              <svg
                                className="w-5 h-5 text-amber-600"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
                              </svg>
                            )}
                          </div>
                          <div>
                            <p className="font-medium text-gray-900 truncate max-w-xs">
                              {processedFile.original.name}
                            </p>
                            <p className="text-sm text-gray-500">
                              {formatFileSize(processedFile.original.size)}
                              {processedFile.compressionResult &&
                                processedFile.compressionResult
                                  .compressionRatio > 0 && (
                                  <span className="text-green-600 ml-2">
                                    (compressed -
                                    {
                                      processedFile.compressionResult
                                        .compressionRatio
                                    }
                                    %)
                                  </span>
                                )}
                            </p>
                          </div>
                        </div>
                        <button
                          onClick={() => setShowConfirmDialog(index)}
                          className="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors"
                          title="Remove file"
                        >
                          <svg
                            className="w-5 h-5"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                            />
                          </svg>
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-800">Total Files</h4>
                  <p className="text-2xl font-bold text-blue-600">
                    {processedFiles.length}
                  </p>
                </div>
                <div className="bg-green-50 rounded-lg p-4">
                  <h4 className="font-semibold text-green-800">Images</h4>
                  <p className="text-2xl font-bold text-green-600">
                    {
                      processedFiles.filter((f) =>
                        f.original.type.startsWith("image/"),
                      ).length
                    }
                  </p>
                </div>
                <div className="bg-purple-50 rounded-lg p-4">
                  <h4 className="font-semibold text-purple-800">Videos</h4>
                  <p className="text-2xl font-bold text-purple-600">
                    {
                      processedFiles.filter((f) =>
                        f.original.type.startsWith("video/"),
                      ).length
                    }
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Confirmation Dialog */}
      {showConfirmDialog !== null && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-60">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              Confirm Removal
            </h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to remove "
              {processedFiles[showConfirmDialog]?.original.name}"? This action
              cannot be undone.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowConfirmDialog(null)}
                className="flex-1 btn-secondary"
              >
                Cancel
              </button>
              <button
                onClick={() => handleRemoveFile(showConfirmDialog)}
                className="flex-1 bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg transition-colors"
              >
                Remove
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

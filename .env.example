# Google Drive API Configuration
# Follow the setup guide in the app to get these values

# Google API Key (from Google Cloud Console)
REACT_APP_GOOGLE_API_KEY=your_api_key_here

# Google OAuth Client ID (from Google Cloud Console)
REACT_APP_GOOGLE_CLIENT_ID=your_client_id_here.apps.googleusercontent.com

# Google Drive Folder ID (from the folder URL in Google Drive)
REACT_APP_GOOGLE_DRIVE_FOLDER_ID=your_folder_id_here

# Admin Panel Configuration
# Admin password for accessing the admin panel (default: wedding2025)
REACT_APP_ADMIN_PASSWORD=your_secure_admin_password_here

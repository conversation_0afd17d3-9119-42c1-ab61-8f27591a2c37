# React + TypeScript + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react) uses [Babel](https://babeljs.io/) (or [oxc](https://oxc.rs) when used in [rolldown-vite](https://vite.dev/guide/rolldown)) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## React Compiler

The React Compiler is not enabled on this template because of its impact on dev & build performances. To add it, see [this documentation](https://react.dev/learn/react-compiler/installation).

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type-aware lint rules:

```js
export default defineConfig([
  globalIgnores(["dist"]),
  {
    files: ["**/*.{ts,tsx}"],
    extends: [
      // Other configs...

      // Remove tseslint.configs.recommended and replace with this
      tseslint.configs.recommendedTypeChecked,
      // Alternatively, use this for stricter rules
      tseslint.configs.strictTypeChecked,
      // Optionally, add this for stylistic rules
      tseslint.configs.stylisticTypeChecked,

      // Other configs...
    ],
    languageOptions: {
      parserOptions: {
        project: ["./tsconfig.node.json", "./tsconfig.app.json"],
        tsconfigRootDir: import.meta.dirname,
      },
      // other options...
    },
  },
]);
```

You can also install [eslint-plugin-react-x](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-x) and [eslint-plugin-react-dom](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-dom) for React-specific lint rules:

```js
// eslint.config.js
import reactX from "eslint-plugin-react-x";
import reactDom from "eslint-plugin-react-dom";

export default defineConfig([
  globalIgnores(["dist"]),
  {
    files: ["**/*.{ts,tsx}"],
    extends: [
      // Other configs...
      // Enable lint rules for React
      reactX.configs["recommended-typescript"],
      // Enable lint rules for React DOM
      reactDom.configs.recommended,
    ],
    languageOptions: {
      parserOptions: {
        project: ["./tsconfig.node.json", "./tsconfig.app.json"],
        tsconfigRootDir: import.meta.dirname,
      },
      // other options...
    },
  },
]);
```

## 🔧 Development Setup

```bash
# Install dependencies
npm install

# Copy environment variables template
cp .env.example .env.local

# Edit .env.local with your Google Drive API credentials
# (Follow the setup guide in the app for detailed instructions)

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## 🔑 Google Drive Setup

The application includes an interactive setup guide accessible through the "Setup Guide" button in the upload interface. This guide will walk you through:

1. Creating a Google Cloud Project
2. Enabling the Google Drive API
3. Setting up OAuth consent screen
4. Creating API credentials
5. Configuring environment variables
6. Setting up the Google Drive folder

**Important:** The Google Drive integration requires proper API credentials. Without these, the app will work for local file processing but won't upload to Google Drive.

## 🚀 Deployment

### Environment Variables

When deploying, make sure to set these environment variables on your hosting platform:

- `REACT_APP_GOOGLE_API_KEY`
- `REACT_APP_GOOGLE_CLIENT_ID`
- `REACT_APP_GOOGLE_DRIVE_FOLDER_ID`

### Platform-Specific Instructions

**Vercel:**

1. Go to your project settings
2. Navigate to Environment Variables
3. Add the three variables with their values

**Netlify:**

1. Go to Site settings
2. Navigate to Environment variables
3. Add the three variables with their values

**Other platforms:**
Check your hosting platform's documentation for setting environment variables.

---

_Built with ❤️ for Karolina & Marcin's special day_
